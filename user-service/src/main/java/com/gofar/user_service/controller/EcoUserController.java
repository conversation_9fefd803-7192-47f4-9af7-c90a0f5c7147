package com.gofar.user_service.controller;

import com.gofar.user_service.dto.EcoUserCreationDto;
import com.gofar.user_service.dto.EcoUserCredentials;
import com.gofar.user_service.dto.EcoUserResponseDto;
import com.gofar.user_service.dto.ValidEcoUserDetails;
import com.gofar.user_service.service.EcoUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/users")
public class EcoUserController {

    private final EcoUserService ecoUserService;

    public EcoUserController(EcoUserService ecoUserService) {
        this.ecoUserService = ecoUserService;
    }

    @GetMapping("/{email}")
    @Operation(summary = "Find user by email", description = "Requires authentication")
    @SecurityRequirement(name = "Bearer Token")
    public ResponseEntity<EcoUserResponseDto> findByEmail(@PathVariable("email") String email) {
        return ecoUserService.findByEmail(email)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    @Operation(summary = "Create new user", description = "Public endpoint - no authentication required")
    public ResponseEntity<EcoUserResponseDto> create(@RequestBody @Valid EcoUserCreationDto dto) {
        EcoUserResponseDto created = ecoUserService.save(dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(created);
    }

    @PostMapping("/validate")
    @Operation(summary = "Validate user credentials", description = "Public endpoint - no authentication required")
    public ResponseEntity<ValidEcoUserDetails> validate(@RequestBody @Valid EcoUserCredentials credentials) {
        ValidEcoUserDetails validated = ecoUserService.validateUser(credentials.email(), credentials.password());
        return validated != null ? ResponseEntity.ok(validated) : ResponseEntity.notFound().build();
    }
}
